import React from 'react';
import { Modal, Descriptions } from 'antd';

const RoomModal = ({ open, onCancel, room }) => {
  const defaultRoom = {
    name: '<PERSON>òng chiếu mặc định',
    capacity: 120,
    screenType: '2D',
    soundSystem: 'Dolby Atmos',
    description: 'Đ<PERSON><PERSON> là mô tả ví dụ cho phòng chiếu.'
  };

  const displayRoom = room || defaultRoom;

  return (
    <Modal
      title="Thông tin phòng chiếu"
      open={open}
      onCancel={onCancel}
      footer={null}
    >
      <Descriptions bordered column={1}>
        <Descriptions.Item label="Tên phòng">{displayRoom.name}</Descriptions.Item>
        <Descriptions.Item label="Sức chứa">{displayRoom.capacity}</Descriptions.Item>
        <Descriptions.Item label="Loại màn hình">{displayRoom.screenType}</Descriptions.Item>
        <Descriptions.Item label="<PERSON>ệ thống âm thanh">{displayRoom.soundSystem}</Descriptions.Item>
        <Descriptions.Item label="M<PERSON> tả">{displayRoom.description}</Descriptions.Item>
      </Descriptions>
    </Modal>
  );
};

export default RoomModal;
