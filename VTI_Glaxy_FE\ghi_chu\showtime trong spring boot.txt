Tôi sẽ giải thích bằng văn bản cách xử lý bài toán đặt lịch chiếu phim trong Spring Boot theo yêu cầu của bạn: "Muốn đặt phim vào một giờ cụ thể thì xử lý ra sao khi lấy danh sách giờ chiếu mà không có dữ liệu, bao gồm các trường hợp một phim một giờ chiếu, một phim nhiều giờ chiếu, hoặc nhiều phim nhiều giờ chiếu, theo best practice." Tôi sẽ giải thích từng phần một cách chi tiết và dễ hiểu.

1. <PERSON>ục tiêu của bài toán
Đặt lịch chiếu phim: Người dùng muốn đặt một bộ phim (được xác định bởi movie_id) vào một phòng cụ thể (room_id) tại một thời điểm cụ thể (start_time).
Xử lý danh sách giờ chiếu: <PERSON><PERSON> lấy danh sách giờ chiếu của một phim mà danh sách rỗng, cần cung cấp giải pháp thay thế (ví dụ: gợi ý giờ trống).
Hỗ trợ các trường hợp:
Một phim, một giờ chiếu: Một phim chỉ có một lịch chiếu duy nhất.
Một phim, nhiều giờ chiếu: Một phim có thể được chiếu nhiều lần ở các giờ khác nhau.
Nhiều phim, nhiều giờ chiếu: Nhiều phim khác nhau được chiếu ở nhiều giờ khác nhau.
Best Practice: Đảm bảo tính toàn vẹn dữ liệu, hiệu suất cao, và trải nghiệm người dùng tốt.
2. Cách tiếp cận tổng quan
Để giải quyết bài toán này trong Spring Boot, tôi sử dụng Spring Data JPA để tương tác với cơ sở dữ liệu MySQL (với các bảng movie, room, và showtime). Dưới đây là các bước chính:

Thiết lập cấu trúc dữ liệu: Xây dựng các entity (Movie, Room, Showtime) để ánh xạ với bảng cơ sở dữ liệu.
Kiểm tra và đặt lịch chiếu: Xử lý yêu cầu đặt lịch, kiểm tra tính hợp lệ (phòng trống, thời gian hợp lý), rồi lưu vào cơ sở dữ liệu.
Lấy danh sách giờ chiếu: Truy vấn giờ chiếu của một phim, nếu không có dữ liệu thì gợi ý giờ trống.
API hóa: Cung cấp các endpoint RESTful để người dùng tương tác (đặt lịch, lấy danh sách giờ chiếu).
3. Chi tiết giải pháp
3.1. Entity (Cấu trúc dữ liệu)
Movie: Đại diện cho bảng movie, chứa thông tin phim như movieId, movieName, duration (thời lượng phim tính bằng phút), v.v.
Room: Đại diện cho bảng room, chứa thông tin phòng chiếu như roomId, name, typeScreen, v.v.
Showtime: Đại diện cho bảng showtime, liên kết phim (movie) và phòng (room) với các trường như startTime, endTime, và status (UPCOMING, ONGOING, FINISHED).
Ví dụ: Một lịch chiếu (Showtime) sẽ lưu thông tin rằng phim "Avengers: Endgame" (movieId = 1) được chiếu ở "Room 1" (roomId = 1) từ 14:00 đến 17:00 ngày 2025-03-23.

3.2. Repository (Truy vấn dữ liệu)
ShowtimeRepository: Giao diện để truy vấn dữ liệu từ bảng showtime.
Kiểm tra chồng chéo thời gian: Dùng truy vấn @Query để kiểm tra xem trong một phòng (roomId) đã có lịch chiếu nào khác trong khoảng thời gian yêu cầu chưa. Nếu có, từ chối đặt lịch.
Lấy giờ chiếu theo phim: Truy vấn tất cả lịch chiếu của một phim (movieId) từ thời điểm hiện tại trở đi.
Lấy giờ chiếu theo phòng: Truy vấn tất cả lịch chiếu trong một phòng để tìm giờ trống.
3.3. DTO (Dữ liệu đầu vào)
ShowtimeRequest: Một lớp đơn giản để nhận dữ liệu từ người dùng khi đặt lịch, bao gồm:
movieId: ID của phim.
roomId: ID của phòng.
startTime: Thời gian bắt đầu (dạng LocalDateTime, ví dụ: "2025-03-23T14:00:00").
3.4. Service (Logic xử lý)
Đây là nơi xử lý chính của bài toán:

3.4.1. Đặt lịch chiếu (scheduleShowtime)
Bước 1: Lấy dữ liệu phim và phòng
Tìm phim theo movieId và phòng theo roomId từ cơ sở dữ liệu. Nếu không tìm thấy, báo lỗi ("Movie not found" hoặc "Room not found").
Bước 2: Tính thời gian kết thúc
Lấy startTime từ yêu cầu người dùng.
Tính endTime = startTime + duration (thời lượng phim lấy từ Movie).
Bước 3: Kiểm tra tính hợp lệ
Đảm bảo endTime > startTime (ràng buộc logic).
Kiểm tra chồng chéo thời gian: Gọi existsOverlappingShowtime để xem phòng đã có lịch chiếu nào trong khoảng startTime đến endTime chưa. Nếu có, trả về thông báo: "Room is not available at this time."
Bước 4: Lưu lịch chiếu
Nếu không có lỗi, tạo một đối tượng Showtime, gán các giá trị (movie, room, startTime, endTime, status = UPCOMING), rồi lưu vào cơ sở dữ liệu.
Trả về thông báo thành công, ví dụ: "Showtime scheduled successfully for Avengers: Endgame at 2025-03-23T14:00".
3.4.2. Lấy danh sách giờ chiếu (getShowtimesByMovie)
Bước 1: Truy vấn giờ chiếu
Lấy tất cả lịch chiếu của phim (movieId) từ thời điểm hiện tại (LocalDateTime.now()) trở đi.
Bước 2: Xử lý trường hợp rỗng
Nếu danh sách rỗng (không có lịch chiếu nào), gọi hàm suggestAvailableTimes để gợi ý giờ trống.
Nếu có dữ liệu, trả về danh sách giờ chiếu (hỗ trợ cả "một phim một giờ" và "một phim nhiều giờ").
Bước 3: Gợi ý giờ trống (suggestAvailableTimes)
Tìm phim theo movieId.
Lấy danh sách tất cả phòng (room).
Duyệt qua từng phòng, kiểm tra xem phòng đó có lịch chiếu nào từ hiện tại trở đi không.
Nếu tìm thấy phòng trống, gợi ý một lịch chiếu mới với:
startTime: Giờ tròn tiếp theo (ví dụ: nếu hiện tại là 13:45, gợi ý 14:00).
endTime: startTime + duration.
Trả về danh sách chỉ chứa một gợi ý này.
3.5. Controller (API)
Đặt lịch chiếu (POST /api/showtimes/schedule):
Nhận ShowtimeRequest từ người dùng qua JSON.
Gọi scheduleShowtime và trả về thông báo kết quả.
Lấy giờ chiếu (GET /api/showtimes/movie/{movieId}):
Nhận movieId từ URL.
Gọi getShowtimesByMovie và trả về danh sách giờ chiếu (hoặc gợi ý nếu rỗng).
4. Cách giải pháp xử lý các trường hợp
4.1. Một phim, một giờ chiếu
Khi gọi GET /api/showtimes/movie/1:
Nếu phim "Avengers: Endgame" (movieId = 1) chỉ có một lịch chiếu (ví dụ: 14:00-17:00 ngày 2025-03-23), API trả về danh sách chỉ chứa một phần tử.
4.2. Một phim, nhiều giờ chiếu
Nếu "Avengers: Endgame" có nhiều lịch chiếu (ví dụ: 14:00-17:00 và 19:00-22:00), API trả về danh sách chứa tất cả các lịch chiếu này.
4.3. Không có dữ liệu (danh sách rỗng)
Nếu phim chưa có lịch chiếu nào, hệ thống gợi ý một giờ trống (ví dụ: "Room 1 trống, gợi ý chiếu từ 14:00-17:00").
Điều này đảm bảo người dùng không bị bỏ rơi mà vẫn có giải pháp thay thế.
4.4. Nhiều phim, nhiều giờ chiếu
Hệ thống hỗ trợ đặt lịch cho nhiều phim khác nhau (movieId khác nhau) ở nhiều phòng và thời gian khác nhau mà không bị xung đột, nhờ kiểm tra chồng chéo thời gian.
5. Best Practice được áp dụng
Tính toàn vẹn dữ liệu: Sử dụng @Transactional trong service để đảm bảo nếu có lỗi khi lưu, toàn bộ thao tác sẽ bị rollback.
Kiểm tra hợp lệ: Kiểm tra dữ liệu đầu vào (phim, phòng, thời gian) trước khi xử lý, tránh lỗi không cần thiết.
Hiệu suất: Dùng truy vấn @Query để kiểm tra chồng chéo thay vì tải toàn bộ dữ liệu rồi lọc, giảm tải cho hệ thống.
Trải nghiệm người dùng: Khi không có lịch chiếu, gợi ý giờ trống thay vì chỉ trả về rỗng, giúp người dùng dễ dàng đặt lịch hơn.
RESTful API: Thiết kế API rõ ràng, dễ sử dụng (POST để tạo, GET để lấy dữ liệu).
Xử lý lỗi: Trả về thông báo thân thiện khi phòng không trống hoặc dữ liệu không hợp lệ.
6. Ví dụ minh họa
Đặt lịch chiếu
Yêu cầu:
json
{
    "movieId": 1,
    "roomId": 1,
    "startTime": "2025-03-23T14:00:00"
}
Kết quả (nếu thành công):

"Showtime scheduled successfully for Avengers: Endgame at 2025-03-23T14:00"
Kết quả (nếu phòng đã có lịch):

"Room is not available at this time. Please choose another time or room."
Lấy danh sách giờ chiếu
Yêu cầu: GET /api/showtimes/movie/1
Kết quả (nếu có dữ liệu):
json
[
    {
        "showtimeId": 1,
        "movie": {"movieId": 1, "movieName": "Avengers: Endgame", "duration": 180, ...},
        "room": {"roomId": 1, "name": "Room 1", ...},
        "startTime": "2025-03-23T14:00:00",
        "endTime": "2025-03-23T17:00:00",
        "status": "UPCOMING"
    }
]
Kết quả (nếu rỗng):
json
[
    {
        "showtimeId": null,
        "movie": {"movieId": 1, "movieName": "Avengers: Endgame", "duration": 180, ...},
        "room": {"roomId": 1, "name": "Room 1", ...},
        "startTime": "2025-03-22T14:00:00",
        "endTime": "2025-03-22T17:00:00",
        "status": "UPCOMING"
    }
]
7. Kết luận
Giải pháp này:

Linh hoạt xử lý cả ba trường hợp (một phim một giờ, một phim nhiều giờ, nhiều phim nhiều giờ).
Đảm bảo phòng không bị đặt trùng thời gian nhờ kiểm tra chồng chéo.
Cải thiện trải nghiệm người dùng bằng cách gợi ý giờ trống khi không có lịch chiếu.
Tuân thủ best practice về hiệu suất, toàn vẹn dữ liệu, và thiết kế API.