<PERSON>ệ thống đặt vé xem phim sử dụng WebSocket trong ví dụ trên có các tính năng chính sau:

1. Đồng bộ thời gian thực
Khi một người dùng đặt một ghế, trạng thái ghế (trống hoặc đã đặt) sẽ được cập nhật ngay lập tức trên giao diện của tất cả người dùng khác đang kết nối. Điều này đảm bảo không ai đặt trùng ghế.
2. Hiển thị trạng thái ghế trực quan
Ghế trống được hiển thị bằng ô màu xám, ghế đã đặt hiển thị màu đỏ.
Người dùng có thể dễ dàng nhận biết ghế nào còn trống để chọn.
3. Tương tác đơn giản
Người dùng chỉ cần nhấp vào ghế trống để đặt. Không cần tải lại trang hay gửi yêu cầu phức tạp.
4. <PERSON><PERSON> lý xung đột
Nếu một ghế đã được đặt, hệ thống sẽ thông báo lỗi ("Ghế đã được đặt!") cho người dùng cố gắng đặt lại ghế đó, tránh tình trạng đặt trùng.
5. Khởi tạo trạng thái ban đầu
Khi một người dùng mới kết nối, họ sẽ nhận được trạng thái hiện tại của tất cả ghế, đảm bảo mọi người đều thấy cùng một thông tin.
6. Hỗ trợ nhiều người dùng
Hệ thống cho phép nhiều client (người dùng) kết nối cùng lúc qua WebSocket, phù hợp với tình huống thực tế như đặt vé xem phim tại rạp.
Giới hạn và tiềm năng mở rộng
Hiện tại, ví dụ này khá cơ bản và chưa có các tính năng nâng cao như:

Xác nhận đặt vé: Có thể thêm bước xác nhận trước khi đặt.
Thông tin phim: Thêm lựa chọn phim, suất chiếu.
Đăng nhập người dùng: Phân biệt các đặt chỗ theo tài khoản.
Lưu dữ liệu: Lưu trạng thái ghế vào cơ sở dữ liệu thay vì chỉ trong bộ nhớ.